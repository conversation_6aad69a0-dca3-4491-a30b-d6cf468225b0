---
type: "always_apply"
---

# معمارية المشروع
## Project Architecture Guide

> **دليل شامل**: هذا المستند يوضح كيفية بناء المشاريع وفقاً لدستور البرمجة المعتمد في هذا المشروع.

---

## 📋 الفهرس
1. [الهيكل العام للمشروع](#general-structure)
2. [أنماط التصميم المعتمدة](#design-patterns)
3. [قواعد الفصل بين الطبقات](#layer-separation)
4. [إدارة الحالة والبيانات](#state-data-management)
5. [استراتيجية النشر والتطوير](#deployment-strategy)
6. [معايير الأداء والقابلية للتوسع](#performance-scalability)
7. [أمان المعمارية](#architecture-security)

---

## 🏗️ الهيكل العام للمشروع {#general-structure}

### البنية الهرمية الإجبارية

```
project-root/
├── 📁 src/                          # الكود المصدري الرئيسي
│   ├── 📁 presentation/             # طبقة العرض (UI/Controllers)
│   │   ├── 📁 components/           # مكونات واجهة المستخدم
│   │   ├── 📁 pages/               # صفحات التطبيق
│   │   ├── 📁 layouts/             # تخطيطات الصفحات
│   │   └── 📁 controllers/         # تحكم في العمليات
│   │
│   ├── 📁 business/                 # طبقة منطق الأعمال
│   │   ├── 📁 services/            # خدمات الأعمال
│   │   ├── 📁 use-cases/           # حالات الاستخدام
│   │   ├── 📁 validators/          # التحقق من صحة البيانات
│   │   └── 📁 processors/          # معالجات البيانات
│   │
│   ├── 📁 data/                     # طبقة البيانات
│   │   ├── 📁 repositories/        # مستودعات البيانات
│   │   ├── 📁 models/              # نماذج البيانات
│   │   ├── 📁 adapters/            # محولات البيانات
│   │   └── 📁 sources/             # مصادر البيانات
│   │
│   ├── 📁 shared/                   # الموارد المشتركة
│   │   ├── 📁 types/               # تعريفات الأنواع
│   │   ├── 📁 constants/           # الثوابت
│   │   ├── 📁 utils/               # الأدوات المساعدة
│   │   ├── 📁 hooks/               # React Hooks المشتركة
│   │   └── 📁 interfaces/          # الواجهات المشتركة
│   │
│   └── 📁 infrastructure/           # البنية التحتية
│       ├── 📁 api/                 # طبقة API
│       ├── 📁 database/            # قاعدة البيانات
│       ├── 📁 cache/               # التخزين المؤقت
│       ├── 📁 logging/             # نظام السجلات
│       └── 📁 monitoring/          # المراقبة والتتبع
│
├── 📁 tests/                        # الاختبارات
│   ├── 📁 unit/                    # اختبارات الوحدة
│   ├── 📁 integration/             # اختبارات التكامل
│   ├── 📁 e2e/                     # اختبارات شاملة
│   └── 📁 performance/             # اختبارات الأداء
│
├── 📁 docs/                         # التوثيق
│   ├── 📁 api/                     # توثيق API
│   ├── 📁 architecture/            # توثيق المعمارية
│   ├── 📁 deployment/              # دليل النشر
│   └── 📁 user-guides/             # أدلة المستخدم
│
├── 📁 config/                       # ملفات التكوين
│   ├── 📁 environments/            # إعدادات البيئات
│   ├── 📁 database/                # إعدادات قاعدة البيانات
│   └── 📁 security/                # إعدادات الأمان
│
├── 📁 scripts/                      # سكريبتات التشغيل
│   ├── 📁 build/                   # سكريبتات البناء
│   ├── 📁 deploy/                  # سكريبتات النشر
│   └── 📁 maintenance/             # سكريبتات الصيانة
│
└── 📁 assets/                       # الموارد الثابتة
    ├── 📁 images/                  # الصور
    ├── 📁 fonts/                   # الخطوط
    └── 📁 styles/                  # ملفات التنسيق
```

### قواعد تنظيم الملفات

#### قاعدة الملف الواحد - المسؤولية الواحدة
```typescript
✅ الصحيح:
// user-service.ts - خدمة المستخدمين فقط
export class UserService {
    async createUser(userData: CreateUserRequest): Promise<User> {}
    async getUserById(id: string): Promise<User | null> {}
    async updateUser(id: string, updates: UpdateUserRequest): Promise<User> {}
    async deleteUser(id: string): Promise<void> {}
}

// user-validator.ts - التحقق من بيانات المستخدمين فقط
export class UserValidator {
    validateCreateRequest(data: CreateUserRequest): ValidationResult {}
    validateUpdateRequest(data: UpdateUserRequest): ValidationResult {}
    validateEmail(email: string): boolean {}
    validatePassword(password: string): boolean {}
}
```

#### قاعدة التصدير المنظم
```typescript
// src/business/services/index.ts
export { UserService } from './user-service';
export { AuthService } from './auth-service';
export { PaymentService } from './payment-service';
export { NotificationService } from './notification-service';

// src/business/index.ts
export * from './services';
export * from './use-cases';
export * from './validators';
export * from './processors';
```

---

## 🎨 أنماط التصميم المعتمدة {#design-patterns}

### 1. Repository Pattern (إجباري لطبقة البيانات)

```typescript
/**
 * واجهة مستودع المستخدمين
 * User Repository Interface
 */
interface IUserRepository {
    create(user: CreateUserData): Promise<User>;
    findById(id: string): Promise<User | null>;
    findByEmail(email: string): Promise<User | null>;
    update(id: string, updates: Partial<User>): Promise<User>;
    delete(id: string): Promise<void>;
    findAll(filters?: UserFilters): Promise<User[]>;
}

/**
 * تنفيذ مستودع المستخدمين
 * User Repository Implementation
 */
export class UserRepository implements IUserRepository {
    constructor(
        private readonly database: IDatabaseAdapter,
        private readonly cache: ICacheAdapter
    ) {}

    async create(userData: CreateUserData): Promise<User> {
        try {
            const user = await this.database.insert('users', userData);
            await this.cache.set(`user:${user.id}`, user, 3600);
            return user;
        } catch (error) {
            throw new RepositoryError('Failed to create user', error);
        }
    }

    async findById(id: string): Promise<User | null> {
        try {
            // محاولة الحصول من التخزين المؤقت أولاً
            const cachedUser = await this.cache.get(`user:${id}`);
            if (cachedUser) {
                return cachedUser;
            }

            // البحث في قاعدة البيانات
            const user = await this.database.findOne('users', { id });
            if (user) {
                await this.cache.set(`user:${id}`, user, 3600);
            }
            
            return user;
        } catch (error) {
            throw new RepositoryError('Failed to find user', error);
        }
    }
}
```

### 2. Service Layer Pattern (إجباري لمنطق الأعمال)

```typescript
/**
 * خدمة إدارة المستخدمين
 * User Management Service
 */
export class UserService {
    constructor(
        private readonly userRepository: IUserRepository,
        private readonly userValidator: IUserValidator,
        private readonly passwordHasher: IPasswordHasher,
        private readonly eventPublisher: IEventPublisher
    ) {}

    /**
     * إنشاء مستخدم جديد
     * Create new user
     */
    async createUser(request: CreateUserRequest): Promise<CreateUserResponse> {
        // التحقق من صحة البيانات
        const validationResult = await this.userValidator.validateCreateRequest(request);
        if (!validationResult.isValid) {
            throw new ValidationError('Invalid user data', validationResult.errors);
        }

        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await this.userRepository.findByEmail(request.email);
        if (existingUser) {
            throw new ConflictError('User with this email already exists');
        }

        // تشفير كلمة المرور
        const hashedPassword = await this.passwordHasher.hash(request.password);

        // إنشاء المستخدم
        const userData: CreateUserData = {
            ...request,
            password: hashedPassword,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const user = await this.userRepository.create(userData);

        // نشر حدث إنشاء المستخدم
        await this.eventPublisher.publish('user.created', {
            userId: user.id,
            email: user.email,
            timestamp: new Date()
        });

        return {
            user: this.mapToUserResponse(user),
            success: true
        };
    }

    /**
     * تحويل بيانات المستخدم للاستجابة
     * Map user data to response
     */
    private mapToUserResponse(user: User): UserResponse {
        return {
            id: user.id,
            name: user.name,
            email: user.email,
            createdAt: user.createdAt,
            // لا نرسل كلمة المرور أبداً
        };
    }
}
```

### 3. Use Case Pattern (إجباري للعمليات المعقدة)

```typescript
/**
 * حالة استخدام: تسجيل مستخدم جديد
 * Use Case: Register New User
 */
export class RegisterUserUseCase {
    constructor(
        private readonly userService: IUserService,
        private readonly emailService: IEmailService,
        private readonly auditLogger: IAuditLogger
    ) {}

    /**
     * تنفيذ عملية التسجيل
     * Execute registration process
     */
    async execute(request: RegisterUserRequest): Promise<RegisterUserResponse> {
        const startTime = Date.now();
        
        try {
            // تسجيل بداية العملية
            await this.auditLogger.log('user.registration.started', {
                email: request.email,
                timestamp: new Date()
            });

            // إنشاء المستخدم
            const createResult = await this.userService.createUser({
                name: request.name,
                email: request.email,
                password: request.password
            });

            // إرسال بريد ترحيبي
            await this.emailService.sendWelcomeEmail({
                to: request.email,
                name: request.name,
                userId: createResult.user.id
            });

            // تسجيل نجاح العملية
            await this.auditLogger.log('user.registration.completed', {
                userId: createResult.user.id,
                email: request.email,
                duration: Date.now() - startTime,
                timestamp: new Date()
            });

            return {
                user: createResult.user,
                success: true,
                message: 'User registered successfully'
            };

        } catch (error) {
            // تسجيل فشل العملية
            await this.auditLogger.log('user.registration.failed', {
                email: request.email,
                error: error.message,
                duration: Date.now() - startTime,
                timestamp: new Date()
            });

            throw error;
        }
    }
}
```

### 4. Factory Pattern (للكائنات المعقدة)

```typescript
/**
 * مصنع إنشاء الخدمات
 * Service Factory
 */
export class ServiceFactory {
    private static instance: ServiceFactory;
    private readonly services: Map<string, any> = new Map();

    private constructor(
        private readonly config: ApplicationConfig,
        private readonly logger: ILogger
    ) {}

    static getInstance(config: ApplicationConfig, logger: ILogger): ServiceFactory {
        if (!ServiceFactory.instance) {
            ServiceFactory.instance = new ServiceFactory(config, logger);
        }
        return ServiceFactory.instance;
    }

    /**
     * إنشاء خدمة المستخدمين
     * Create User Service
     */
    createUserService(): IUserService {
        const cacheKey = 'userService';
        
        if (this.services.has(cacheKey)) {
            return this.services.get(cacheKey);
        }

        // إنشاء التبعيات
        const databaseAdapter = this.createDatabaseAdapter();
        const cacheAdapter = this.createCacheAdapter();
        const userRepository = new UserRepository(databaseAdapter, cacheAdapter);
        const userValidator = new UserValidator();
        const passwordHasher = new PasswordHasher(this.config.security.saltRounds);
        const eventPublisher = this.createEventPublisher();

        // إنشاء الخدمة
        const userService = new UserService(
            userRepository,
            userValidator,
            passwordHasher,
            eventPublisher
        );

        this.services.set(cacheKey, userService);
        return userService;
    }

    /**
     * إنشاء محول قاعدة البيانات
     * Create Database Adapter
     */
    private createDatabaseAdapter(): IDatabaseAdapter {
        switch (this.config.database.type) {
            case 'postgresql':
                return new PostgreSQLAdapter(this.config.database.postgresql);
            case 'mongodb':
                return new MongoDBAdapter(this.config.database.mongodb);
            case 'mysql':
                return new MySQLAdapter(this.config.database.mysql);
            default:
                throw new Error(`Unsupported database type: ${this.config.database.type}`);
        }
    }
}
```

---

## 🔄 قواعد الفصل بين الطبقات {#layer-separation}

### قاعدة الاعتماد الأحادي الاتجاه

```
📊 اتجاه الاعتماد (Dependency Direction):

Presentation Layer
        ↓
Business Layer  
        ↓
Data Layer
        ↓
Infrastructure Layer

❌ ممنوع: طبقة أدنى تعتمد على طبقة أعلى
✅ مسموح: طبقة أعلى تعتمد على طبقة أدنى عبر الواجهات
```

### تطبيق Dependency Inversion

```typescript
// ❌ خطأ: الاعتماد المباشر على التنفيذ
export class UserService {
    private userRepository = new UserRepository(); // اعتماد مباشر
    
    async getUser(id: string): Promise<User> {
        return this.userRepository.findById(id);
    }
}

// ✅ صحيح: الاعتماد على الواجهة
export class UserService {
    constructor(
        private readonly userRepository: IUserRepository // حقن التبعية
    ) {}
    
    async getUser(id: string): Promise<User> {
        return this.userRepository.findById(id);
    }
}
```

### واجهات الطبقات

```typescript
/**
 * واجهات طبقة العرض
 * Presentation Layer Interfaces
 */
export interface IController {
    handle(request: Request): Promise<Response>;
}

export interface IMiddleware {
    execute(request: Request, next: NextFunction): Promise<void>;
}

/**
 * واجهات طبقة الأعمال
 * Business Layer Interfaces
 */
export interface IService<TRequest, TResponse> {
    execute(request: TRequest): Promise<TResponse>;
}

export interface IValidator<T> {
    validate(data: T): ValidationResult;
}

export interface IUseCase<TRequest, TResponse> {
    execute(request: TRequest): Promise<TResponse>;
}

/**
 * واجهات طبقة البيانات
 * Data Layer Interfaces
 */
export interface IRepository<T, TKey = string> {
    create(entity: Omit<T, 'id'>): Promise<T>;
    findById(id: TKey): Promise<T | null>;
    update(id: TKey, updates: Partial<T>): Promise<T>;
    delete(id: TKey): Promise<void>;
    findAll(filters?: any): Promise<T[]>;
}

export interface IDataAdapter<T> {
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    query(sql: string, params?: any[]): Promise<T[]>;
    insert(table: string, data: any): Promise<T>;
    update(table: string, id: any, data: any): Promise<T>;
    delete(table: string, id: any): Promise<void>;
}
```

---

## 📊 إدارة الحالة والبيانات {#state-data-management}

### نمط إدارة الحالة المعتمد

```typescript
/**
 * مخزن الحالة العام
 * Global State Store
 */
export interface ApplicationState {
    user: UserState;
    ui: UIState;
    data: DataState;
    errors: ErrorState;
}

export interface UserState {
    currentUser: User | null;
    isAuthenticated: boolean;
    permissions: Permission[];
    preferences: UserPreferences;
}

export interface UIState {
    isLoading: boolean;
    notifications: Notification[];
    modals: ModalState[];
    theme: ThemeSettings;
}

/**
 * إدارة حالة المستخدم
 * User State Management
 */
export class UserStateManager {
    private state: UserState = {
        currentUser: null,
        isAuthenticated: false,
        permissions: [],
        preferences: {}
    };

    private listeners: Array<(state: UserState) => void> = [];

    /**
     * تحديث حالة المستخدم
     * Update user state
     */
    updateUserState(updates: Partial<UserState>): void {
        const previousState = { ...this.state };
        this.state = { ...this.state, ...updates };
        
        // إشعار المستمعين بالتغيير
        this.notifyListeners(previousState, this.state);
    }

    /**
     * الحصول على الحالة الحالية
     * Get current state
     */
    getCurrentState(): UserState {
        return { ...this.state };
    }

    /**
     * الاشتراك في تغييرات الحالة
     * Subscribe to state changes
     */
    subscribe(listener: (state: UserState) => void): () => void {
        this.listeners.push(listener);
        
        // إرجاع دالة إلغاء الاشتراك
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    /**
     * إشعار المستمعين بالتغييرات
     * Notify listeners of changes
     */
    private notifyListeners(previousState: UserState, newState: UserState): void {
        this.listeners.forEach(listener => {
            try {
                listener(newState);
            } catch (error) {
                console.error('Error in state listener:', error);
            }
        });
    }
}
```

### نمط Command للعمليات

```typescript
/**
 * واجهة الأوامر
 * Command Interface
 */
export interface ICommand<TRequest, TResponse> {
    execute(request: TRequest): Promise<TResponse>;
    undo?(): Promise<void>;
    canExecute(request: TRequest): boolean;
}

/**
 * أمر إنشاء المستخدم
 * Create User Command
 */
export class CreateUserCommand implements ICommand<CreateUserRequest, CreateUserResponse> {
    private createdUserId?: string;

    constructor(
        private readonly userService: IUserService,
        private readonly auditLogger: IAuditLogger
    ) {}

    canExecute(request: CreateUserRequest): boolean {
        return !!(request.name && request.email && request.password);
    }

    async execute(request: CreateUserRequest): Promise<CreateUserResponse> {
        if (!this.canExecute(request)) {
            throw new ValidationError('Invalid create user request');
        }

        const result = await this.userService.createUser(request);
        this.createdUserId = result.user.id;

        await this.auditLogger.log('command.create_user.executed', {
            userId: result.user.id,
            timestamp: new Date()
        });

        return result;
    }

    async undo(): Promise<void> {
        if (this.createdUserId) {
            await this.userService.deleteUser(this.createdUserId);
            
            await this.auditLogger.log('command.create_user.undone', {
                userId: this.createdUserId,
                timestamp: new Date()
            });
        }
    }
}

/**
 * منفذ الأوامر
 * Command Executor
 */
export class CommandExecutor {
    private commandHistory: Array<ICommand<any, any>> = [];

    async execute<TRequest, TResponse>(
        command: ICommand<TRequest, TResponse>,
        request: TRequest
    ): Promise<TResponse> {
        try {
            const result = await command.execute(request);
            
            // حفظ الأمر في التاريخ للتراجع المحتمل
            if (command.undo) {
                this.commandHistory.push(command);
            }
            
            return result;
        } catch (error) {
            throw new CommandExecutionError('Failed to execute command', error);
        }
    }

    async undoLastCommand(): Promise<void> {
        const lastCommand = this.commandHistory.pop();
        if (lastCommand && lastCommand.undo) {
            await lastCommand.undo();
        }
    }
}
```

---

## 🚀 استراتيجية النشر والتطوير {#deployment-strategy}

### بيئات التطوير

```typescript
/**
 * إعدادات البيئات
 * Environment Configuration
 */
export interface EnvironmentConfig {
    name: 'development' | 'staging' | 'production';
    database: DatabaseConfig;
    api: ApiConfig;
    security: SecurityConfig;
    logging: LoggingConfig;
    monitoring: MonitoringConfig;
}

/**
 * إعدادات بيئة التطوير
 * Development Environment Config
 */
export const developmentConfig: EnvironmentConfig = {
    name: 'development',
    database: {
        type: 'postgresql',
        host: 'localhost',
        port: 5432,
        database: 'app_dev',
        username: process.env.DB_USERNAME || 'dev_user',
        password: process.env.DB_PASSWORD || 'dev_password',
        ssl: false,
        logging: true
    },
    api: {
        port: 3000,
        cors: {
            origin: ['http://localhost:3000', 'http://localhost:3001'],
            credentials: true
        },
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 1000 // requests per window
        }
    },
    security: {
        jwtSecret: process.env.JWT_SECRET || 'dev-secret-key',
        bcryptRounds: 10,
        sessionTimeout: 24 * 60 * 60 * 1000 // 24 hours
    },
    logging: {
        level: 'debug',
        format: 'detailed',
        outputs: ['console', 'file']
    },
    monitoring: {
        enabled: true,
        metricsInterval: 30000, // 30 seconds
        healthCheckInterval: 10000 // 10 seconds
    }
};

/**
 * إعدادات بيئة الإنتاج
 * Production Environment Config
 */
export const productionConfig: EnvironmentConfig = {
    name: 'production',
    database: {
        type: 'postgresql',
        host: process.env.DB_HOST!,
        port: parseInt(process.env.DB_PORT || '5432'),
        database: process.env.DB_NAME!,
        username: process.env.DB_USERNAME!,
        password: process.env.DB_PASSWORD!,
        ssl: true,
        logging: false,
        pool: {
            min: 5,
            max: 20,
            acquireTimeoutMillis: 30000,
            idleTimeoutMillis: 30000
        }
    },
    api: {
        port: parseInt(process.env.PORT || '8080'),
        cors: {
            origin: process.env.ALLOWED_ORIGINS?.split(',') || [],
            credentials: true
        },
        rateLimit: {
            windowMs: 15 * 60 * 1000,
            max: 100 // more restrictive in production
        }
    },
    security: {
        jwtSecret: process.env.JWT_SECRET!,
        bcryptRounds: 12, // more secure in production
        sessionTimeout: 2 * 60 * 60 * 1000 // 2 hours
    },
    logging: {
        level: 'info',
        format: 'json',
        outputs: ['file', 'external']
    },
    monitoring: {
        enabled: true,
        metricsInterval: 60000, // 1 minute
        healthCheckInterval: 30000, // 30 seconds
        alerting: {
            enabled: true,
            thresholds: {
                errorRate: 0.05, // 5%
                responseTime: 2000, // 2 seconds
                memoryUsage: 0.85 // 85%
            }
        }
    }
};
```

### استراتيجية CI/CD

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run type checking
      run: npm run type-check
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Check test coverage
      run: npm run test:coverage
    
    - name: Run security audit
      run: npm audit --audit-level high

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Build Docker image
      run: docker build -t app:${{ github.sha }} .
    
    - name: Run security scan
      run: docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image app:${{ github.sha }}

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment"
        # Deployment commands here
    
    - name: Run E2E tests
      run: npm run test:e2e:staging

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment"
        # Deployment commands here
    
    - name: Run smoke tests
      run: npm run test:smoke:production
```

---

## ⚡ معايير الأداء والقابلية للتوسع {#performance-scalability}

### معايير الأداء الإجبارية

```typescript
/**
 * معايير الأداء المطلوبة
 * Required Performance Metrics
 */
export const PERFORMANCE_REQUIREMENTS = {
    // زمن الاستجابة
    RESPONSE_TIME: {
        API_ENDPOINTS: {
            GET: 200, // milliseconds
            POST: 500,
            PUT: 500,
            DELETE: 300
        },
        DATABASE_QUERIES: {
            SIMPLE_SELECT: 50,
            COMPLEX_JOIN: 200,
            INSERT: 100,
            UPDATE: 150
        },
        CACHE_OPERATIONS: {
            GET: 10,
            SET: 20,
            DELETE: 15
        }
    },
    
    // الإنتاجية
    THROUGHPUT: {
        REQUESTS_PER_SECOND: 1000,
        CONCURRENT_USERS: 500,
        DATABASE_CONNECTIONS: 20
    },
    
    // استخدام الموارد
    RESOURCE_USAGE: {
        MEMORY_LIMIT: '512MB',
        CPU_LIMIT: '80%',
        DISK_SPACE: '10GB'
    }
};

/**
 * مراقب الأداء
 * Performance Monitor
 */
export class PerformanceMonitor {
    private metrics: Map<string, PerformanceMetric[]> = new Map();

    /**
     * قياس زمن تنفيذ العملية
     * Measure operation execution time
     */
    async measureExecutionTime<T>(
        operationName: string,
        operation: () => Promise<T>
    ): Promise<T> {
        const startTime = Date.now();
        const startMemory = process.memoryUsage();

        try {
            const result = await operation();
            const endTime = Date.now();
            const endMemory = process.memoryUsage();

            const metric: PerformanceMetric = {
                operationName,
                executionTime: endTime - startTime,
                memoryUsage: {
                    before: startMemory,
                    after: endMemory,
                    delta: {
                        rss: endMemory.rss - startMemory.rss,
                        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
                        heapTotal: endMemory.heapTotal - startMemory.heapTotal
                    }
                },
                timestamp: new Date(),
                success: true
            };

            this.recordMetric(operationName, metric);
            this.checkPerformanceThresholds(metric);

            return result;
        } catch (error) {
            const endTime = Date.now();
            
            const metric: PerformanceMetric = {
                operationName,
                executionTime: endTime - startTime,
                timestamp: new Date(),
                success: false,
                error: error.message
            };

            this.recordMetric(operationName, metric);
            throw error;
        }
    }

    /**
     * فحص حدود الأداء
     * Check performance thresholds
     */
    private checkPerformanceThresholds(metric: PerformanceMetric): void {
        const thresholds = this.getThresholdsForOperation(metric.operationName);
        
        if (metric.executionTime > thresholds.maxExecutionTime) {
            console.warn(`Performance warning: ${metric.operationName} took ${metric.executionTime}ms (threshold: ${thresholds.maxExecutionTime}ms)`);
        }

        if (metric.memoryUsage?.delta.heapUsed && metric.memoryUsage.delta.heapUsed > thresholds.maxMemoryDelta) {
            console.warn(`Memory warning: ${metric.operationName} used ${metric.memoryUsage.delta.heapUsed} bytes (threshold: ${thresholds.maxMemoryDelta} bytes)`);
        }
    }

    /**
     * الحصول على حدود الأداء للعملية
     * Get performance thresholds for operation
     */
    private getThresholdsForOperation(operationName: string): PerformanceThresholds {
        // تحديد الحدود بناءً على نوع العملية
        if (operationName.includes('database')) {
            return {
                maxExecutionTime: PERFORMANCE_REQUIREMENTS.RESPONSE_TIME.DATABASE_QUERIES.COMPLEX_JOIN,
                maxMemoryDelta: 50 * 1024 * 1024 // 50MB
            };
        }
        
        if (operationName.includes('api')) {
            return {
                maxExecutionTime: PERFORMANCE_REQUIREMENTS.RESPONSE_TIME.API_ENDPOINTS.GET,
                maxMemoryDelta: 10 * 1024 * 1024 // 10MB
            };
        }

        return {
            maxExecutionTime: 1000, // 1 second default
            maxMemoryDelta: 100 * 1024 * 1024 // 100MB default
        };
    }
}
```

### استراتيجية التخزين المؤقت

```typescript
/**
 * مدير التخزين المؤقت متعدد الطبقات
 * Multi-layer Cache Manager
 */
export class CacheManager {
    constructor(
        private readonly memoryCache: IMemoryCache,
        private readonly redisCache: IRedisCache,
        private readonly performanceMonitor: PerformanceMonitor
    ) {}

    /**
     * الحصول على البيانات مع استراتيجية التخزين المؤقت
     * Get data with caching strategy
     */
    async get<T>(key: string, fallback: () => Promise<T>, options?: CacheOptions): Promise<T> {
        return this.performanceMonitor.measureExecutionTime(
            `cache.get.${key}`,
            async () => {
                // المحاولة الأولى: الذاكرة المحلية
                const memoryResult = await this.memoryCache.get<T>(key);
                if (memoryResult !== null) {
                    return memoryResult;
                }

                // المحاولة الثانية: Redis
                const redisResult = await this.redisCache.get<T>(key);
                if (redisResult !== null) {
                    // حفظ في الذاكرة المحلية للمرة القادمة
                    await this.memoryCache.set(key, redisResult, options?.memoryTtl || 300);
                    return redisResult;
                }

                // المحاولة الأخيرة: تنفيذ الدالة الأصلية
                const result = await fallback();
                
                // حفظ في جميع طبقات التخزين المؤقت
                await Promise.all([
                    this.memoryCache.set(key, result, options?.memoryTtl || 300),
                    this.redisCache.set(key, result, options?.redisTtl || 3600)
                ]);

                return result;
            }
        );
    }

    /**
     * إبطال التخزين المؤقت
     * Invalidate cache
     */
    async invalidate(pattern: string): Promise<void> {
        await Promise.all([
            this.memoryCache.invalidate(pattern),
            this.redisCache.invalidate(pattern)
        ]);
    }
}
```

---

## 🔒 أمان المعمارية {#architecture-security}

### طبقات الأمان الإجبارية

```typescript
/**
 * مدير الأمان الشامل
 * Comprehensive Security Manager
 */
export class SecurityManager {
    constructor(
        private readonly authService: IAuthService,
        private readonly encryptionService: IEncryptionService,
        private readonly auditLogger: IAuditLogger,
        private readonly rateLimiter: IRateLimiter
    ) {}

    /**
     * التحقق من الصلاحيات
     * Authorization check
     */
    async checkPermission(
        user: User,
        resource: string,
        action: string
    ): Promise<boolean> {
        try {
            // التحقق من صحة المستخدم
            if (!user || !user.id) {
                await this.auditLogger.log('security.unauthorized_access_attempt', {
                    resource,
                    action,
                    timestamp: new Date()
                });
                return false;
            }

            // التحقق من الصلاحيات
            const hasPermission = await this.authService.hasPermission(user.id, resource, action);
            
            if (!hasPermission) {
                await this.auditLogger.log('security.insufficient_permissions', {
                    userId: user.id,
                    resource,
                    action,
                    timestamp: new Date()
                });
            }

            return hasPermission;
        } catch (error) {
            await this.auditLogger.log('security.permission_check_error', {
                userId: user?.id,
                resource,
                action,
                error: error.message,
                timestamp: new Date()
            });
            return false;
        }
    }

    /**
     * تشفير البيانات الحساسة
     * Encrypt sensitive data
     */
    async encryptSensitiveData(data: any): Promise<EncryptedData> {
        const sensitiveFields = this.identifySensitiveFields(data);
        const encryptedData = { ...data };

        for (const field of sensitiveFields) {
            if (encryptedData[field]) {
                encryptedData[field] = await this.encryptionService.encrypt(
                    encryptedData[field].toString()
                );
            }
        }

        return {
            data: encryptedData,
            encryptedFields: sensitiveFields,
            encryptionTimestamp: new Date()
        };
    }

    /**
     * فحص معدل الطلبات
     * Rate limiting check
     */
    async checkRateLimit(
        identifier: string,
        action: string
    ): Promise<RateLimitResult> {
        const result = await this.rateLimiter.check(identifier, action);
        
        if (!result.allowed) {
            await this.auditLogger.log('security.rate_limit_exceeded', {
                identifier,
                action,
                remainingRequests: result.remaining,
                resetTime: result.resetTime,
                timestamp: new Date()
            });
        }

        return result;
    }

    /**
     * تحديد الحقول الحساسة
     * Identify sensitive fields
     */
    private identifySensitiveFields(data: any): string[] {
        const sensitivePatterns = [
            /password/i,
            /secret/i,
            /token/i,
            /key/i,
            /ssn/i,
            /credit.*card/i,
            /bank.*account/i
        ];

        return Object.keys(data).filter(key =>
            sensitivePatterns.some(pattern => pattern.test(key))
        );
    }
}

/**
 * حماية نقاط النهاية
 * API Endpoint Protection
 */
export function SecureEndpoint(
    permissions: string[],
    rateLimit?: RateLimitConfig
) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;

        descriptor.value = async function (...args: any[]) {
            const request = args[0] as AuthenticatedRequest;
            const securityManager = container.get<SecurityManager>('SecurityManager');

            // فحص معدل الطلبات
            if (rateLimit) {
                const rateLimitResult = await securityManager.checkRateLimit(
                    request.user?.id || request.ip,
                    `${target.constructor.name}.${propertyName}`
                );

                if (!rateLimitResult.allowed) {
                    throw new RateLimitExceededError('Rate limit exceeded');
                }
            }

            // فحص الصلاحيات
            for (const permission of permissions) {
                const [resource, action] = permission.split(':');
                const hasPermission = await securityManager.checkPermission(
                    request.user,
                    resource,
                    action
                );

                if (!hasPermission) {
                    throw new UnauthorizedError(`Missing permission: ${permission}`);
                }
            }

            // تنفيذ الدالة الأصلية
            return method.apply(this, args);
        };

        return descriptor;
    };
}
```

---

## 🎯 الخلاصة

هذه المعمارية تضمن:

### ✅ الجودة والموثوقية
- فصل واضح بين الطبقات
- أنماط تصميم مثبتة
- اختبارات شاملة على جميع المستويات

### ✅ الأداء والقابلية للتوسع
- تخزين مؤقت متعدد الطبقات
- مراقبة الأداء المستمرة
- تحسين استخدام الموارد

### ✅ الأمان والحماية
- طبقات أمان متعددة
- تشفير البيانات الحساسة
- مراقبة ومراجعة شاملة

### ✅ سهولة الصيانة والتطوير
- كود منظم وقابل للقراءة
- توثيق شامل
- أدوات تطوير متقدمة

---

**تاريخ الإنشاء**: 2024-01-01  
**الإصدار**: 1.0.0  
**المؤلف**: AI Assistant  
**الحالة**: دليل معتمد للمعمارية
