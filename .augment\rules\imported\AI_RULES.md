---
type: "always_apply"
---

# دستور البرمجة الذكي للذكاء الاصطناعي
## Smart AI Programming Constitution

> **مبدأ**: هذا المستند يحتوي على القواعد العملية والذكية للبرمجة الفعالة. الهدف هو إنتاج كود عالي الجودة بسرعة وكفاءة، مع التفكير كمهندس حقيقي وإدارة المشاريع بطريقة منهجية.

---

## 📋 الفهرس
1. [المبادئ الأساسية](#core-principles)
2. [منهجية إدارة المشاريع](#project-management)
3. [قواعد التسمية والتنظيم](#naming-rules)
4. [بنية المشروع الإجبارية](#project-structure)
5. [معايير الجودة الذكية](#smart-quality)
6. [قواعد الحماية والأمان](#essential-security)
7. [متطلبات التوثيق](#practical-documentation)
8. [معايير الأداء والاختبار](#smart-testing)
9. [التفكير الهندسي](#engineering-mindset)
10. [الخلاصة والمبادئ النهائية](#conclusion)

---

## 🎯 المبادئ الأساسية {#core-principles}

### مبادئ التفكير الهندسي الذكي

#### 1. فهم المشكلة أولاً
- **اقرأ وافهم** المطلوب بالكامل قبل البدء
- **حلل المتطلبات** وحدد الأهداف الواضحة
- **اسأل الأسئلة المهمة** إذا كان هناك غموض
- **تصور النتيجة النهائية** قبل البدء في التنفيذ

#### 2. التخطيط الذكي
- **قسم المشروع** إلى مراحل منطقية
- **حدد الأولويات** - ما هو الأهم أولاً؟
- **اختر التقنيات المناسبة** بناءً على المتطلبات
- **خطط للاختبار** من البداية

#### 3. التنفيذ المنهجي
- **ابدأ بالأساسيات** - MVP أولاً
- **اختبر كل خطوة** قبل الانتقال للتالية
- **وثق القرارات المهمة** أثناء التطوير
- **راجع التقدم** بانتظام

#### 4. الجودة العملية
- **أمان أساسي** - لا تساوم على الأمان
- **كود واضح** - يجب أن يفهمه مطور آخر
- **أداء مقبول** - حسن عند الحاجة فقط
- **اختبارات ذكية** - للأجزاء المهمة

---

## 📊 منهجية إدارة المشاريع {#project-management}

### خطوات التخطيط الشامل

#### المرحلة 1: تحليل المشروع (إجباري)
```
🔍 أسئلة التحليل الأساسية:
1. ما هو الهدف الرئيسي للمشروع؟
2. من هم المستخدمون المستهدفون؟
3. ما هي المتطلبات الوظيفية الأساسية؟
4. ما هي القيود التقنية أو الزمنية؟
5. ما هي معايير النجاح؟

📋 مخرجات هذه المرحلة:
- وثيقة متطلبات واضحة
- قائمة بالميزات الأساسية
- تحديد التقنيات المطلوبة
- تقدير الوقت والجهد
```

#### المرحلة 2: التصميم المعماري
```
🏗️ عناصر التصميم:
1. بنية المشروع (folders, files)
2. تدفق البيانات (data flow)
3. واجهات المستخدم الأساسية
4. قاعدة البيانات (إذا لزم)
5. APIs والتكاملات الخارجية

📐 مخرجات هذه المرحلة:
- مخطط بنية المشروع
- تصميم قاعدة البيانات
- تحديد المكونات الرئيسية
- خطة التطوير المرحلية
```

#### المرحلة 3: خطة التنفيذ المرحلية
```
🚀 تقسيم العمل:
المرحلة الأولى (MVP):
- الميزات الأساسية فقط
- واجهة مستخدم بسيطة
- وظائف أساسية تعمل

المرحلة الثانية (Enhancement):
- تحسين واجهة المستخدم
- إضافة ميزات متقدمة
- تحسين الأداء

المرحلة الثالثة (Polish):
- اختبارات شاملة
- تحسينات الأمان
- توثيق نهائي
```

### نظام تتبع التقدم

#### Checklist لكل مرحلة
```
✅ قبل بدء أي مرحلة:
- [ ] فهمت المتطلبات بوضوح
- [ ] حددت المخرجات المطلوبة
- [ ] خططت للاختبار
- [ ] جهزت البيئة التطويرية

✅ أثناء التطوير:
- [ ] أختبر كل ميزة بعد تطويرها
- [ ] أوثق القرارات المهمة
- [ ] أراجع الكود بانتظام
- [ ] أتأكد من الأمان الأساسي

✅ عند إنهاء المرحلة:
- [ ] جميع الميزات تعمل كما هو مطلوب
- [ ] الاختبارات تمر بنجاح
- [ ] الكود موثق بشكل مناسب
- [ ] جاهز للمرحلة التالية
```

### إدارة السياق والذاكرة

#### تسجيل القرارات المهمة
```typescript
/**
 * سجل القرارات - يجب تحديثه عند كل قرار مهم
 * Decision Log - Must be updated with every important decision
 */

// مثال:
/*
DECISION LOG:
- 2024-07-29: اخترت React بدلاً من Vue لأن المشروع يحتاج مكونات معقدة
- 2024-07-29: استخدمت TypeScript للحصول على type safety
- 2024-07-29: قررت استخدام MongoDB لمرونة البيانات
*/
```

#### نقاط المراجعة الإجبارية
```
🔄 مراجعة كل 3 ملفات:
- هل ما زلت على المسار الصحيح؟
- هل تحقق الأهداف المحددة؟
- هل تحتاج تعديل الخطة؟

🔄 مراجعة كل ميزة:
- هل تعمل كما هو مطلوب؟
- هل تتكامل مع باقي النظام؟
- هل تحتاج تحسينات؟

🔄 مراجعة نهاية كل مرحلة:
- هل حققت أهداف المرحلة؟
- ما الدروس المستفادة؟
- كيف يمكن تحسين المرحلة التالية؟
```

---

## 🏷️ قواعد التسمية والتنظيم {#naming-rules}

### قواعد التسمية الإجبارية

#### الملفات والمجلدات
```
✅ الصحيح:
- user-service.ts
- api-client.js
- data-processor.py
- user-management/
- api-endpoints/

❌ الخطأ:
- UserService.ts
- apiClient.js
- dataProcessor.py
- userManagement/
- API_endpoints/
```

#### المتغيرات والدوال
```typescript
✅ الصحيح:
const userName = "john";
const isValidEmail = true;
function getUserData() {}
function calculateTotalPrice() {}

❌ الخطأ:
const user_name = "john";
const IsValidEmail = true;
function get_user_data() {}
function Calculate_Total_Price() {}
```

#### الكلاسات والواجهات
```typescript
✅ الصحيح:
class UserService {}
interface ApiResponse {}
type UserDataType = {};
enum OrderStatus {}

❌ الخطأ:
class userService {}
interface apiResponse {}
type userDataType = {};
enum orderStatus {}
```

#### الثوابت
```typescript
✅ الصحيح:
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = "https://api.example.com";
const DEFAULT_TIMEOUT = 5000;

❌ الخطأ:
const maxRetryCount = 3;
const apiBaseUrl = "https://api.example.com";
const defaultTimeout = 5000;
```

### قواعد التنظيم الإجبارية

#### تجميع الملفات
- كل ملف يجب أن يحتوي على وظيفة واحدة محددة
- الملفات المترابطة يجب أن تكون في نفس المجلد
- استخدام index.ts لتصدير الوحدات من المجلد

#### تسمية الدوال
- الدوال يجب أن تبدأ بفعل (get, set, create, update, delete, validate, calculate)
- الدوال المنطقية يجب أن تبدأ بـ (is, has, can, should, will)
- الدوال غير المتزامنة يجب أن تنتهي بـ Async

```typescript
✅ الصحيح:
function getUserById(id: string): User {}
function isValidEmail(email: string): boolean {}
function hasPermission(user: User): boolean {}
async function fetchUserDataAsync(id: string): Promise<User> {}

❌ الخطأ:
function user(id: string): User {}
function validEmail(email: string): boolean {}
function permission(user: User): boolean {}
async function fetchUserData(id: string): Promise<User> {}
```

---

## 🏗️ بنية المشروع الإجبارية {#project-structure}

### الهيكل الأساسي الإجباري
```
project-root/
├── src/                    # الكود المصدري
│   ├── components/         # المكونات القابلة لإعادة الاستخدام
│   ├── services/          # خدمات الأعمال
│   ├── utils/             # الأدوات المساعدة
│   ├── types/             # تعريفات الأنواع
│   ├── constants/         # الثوابت
│   ├── hooks/             # React Hooks (للمشاريع React)
│   ├── stores/            # إدارة الحالة
│   ├── api/               # طبقة API
│   └── tests/             # اختبارات الوحدة
├── docs/                  # التوثيق
├── config/                # ملفات التكوين
├── scripts/               # سكريبتات البناء والنشر
├── public/                # الملفات العامة (للتطبيقات الويب)
├── tests/                 # اختبارات التكامل
└── README.md              # وثيقة المشروع الرئيسية
```

### قواعد تنظيم المجلدات

#### مجلد src/
- **components/**: مكونات UI قابلة لإعادة الاستخدام
- **services/**: منطق الأعمال والخدمات
- **utils/**: دوال مساعدة عامة
- **types/**: تعريفات TypeScript
- **constants/**: قيم ثابتة
- **api/**: طبقة التواصل مع الخادم

#### قواعد إضافية
- كل مجلد يجب أن يحتوي على index.ts
- الملفات المترابطة في مجلد فرعي
- عدم تجاوز 10 ملفات في المجلد الواحد
- استخدام barrel exports

```typescript
// src/services/index.ts
export { UserService } from './user-service';
export { AuthService } from './auth-service';
export { ApiService } from './api-service';
```

---

## ⚡ معايير جودة الكود {#code-quality}

### قواعد TypeScript الإجبارية

#### استخدام الأنواع
```typescript
✅ الصحيح:
function calculateTotal(items: CartItem[]): number {
    return items.reduce((total: number, item: CartItem) => {
        return total + (item.price * item.quantity);
    }, 0);
}

❌ الخطأ:
function calculateTotal(items: any): any {
    return items.reduce((total, item) => {
        return total + (item.price * item.quantity);
    }, 0);
}
```

#### منع استخدام any
```typescript
❌ ممنوع تماماً:
const userData: any = {};
function processData(data: any): any {}

✅ البديل الصحيح:
interface UserData {
    id: string;
    name: string;
    email: string;
}
const userData: UserData = {};
function processData(data: UserData): ProcessedData {}
```

### قواعد بنية الكود

#### حجم الدوال
- كل دالة يجب أن تكون أقل من 20 سطر
- إذا تجاوزت 20 سطر، يجب تقسيمها

```typescript
✅ الصحيح:
function validateUser(user: User): ValidationResult {
    const emailValidation = validateEmail(user.email);
    const passwordValidation = validatePassword(user.password);
    const nameValidation = validateName(user.name);
    
    return {
        isValid: emailValidation.isValid && passwordValidation.isValid && nameValidation.isValid,
        errors: [...emailValidation.errors, ...passwordValidation.errors, ...nameValidation.errors]
    };
}

function validateEmail(email: string): ValidationResult {
    // منطق التحقق من البريد الإلكتروني
}
```

#### حجم الملفات
- كل ملف يجب أن يكون أقل من 200 سطر
- إذا تجاوز 200 سطر، يجب تقسيمه

#### Pure Functions
```typescript
✅ الصحيح (Pure Function):
function calculateTax(amount: number, rate: number): number {
    return amount * rate;
}

❌ الخطأ (Side Effects):
let globalTaxRate = 0.1;
function calculateTax(amount: number): number {
    globalTaxRate += 0.01; // تعديل متغير خارجي
    return amount * globalTaxRate;
}
```

### قواعد معالجة الأخطاء

#### استخدام Try-Catch الإجباري
```typescript
✅ الصحيح:
async function fetchUserData(id: string): Promise<User | null> {
    try {
        const response = await apiClient.get(`/users/${id}`);
        return response.data;
    } catch (error) {
        logger.error('Failed to fetch user data', { id, error });
        return null;
    }
}

❌ الخطأ:
async function fetchUserData(id: string): Promise<User> {
    const response = await apiClient.get(`/users/${id}`); // بدون معالجة أخطاء
    return response.data;
}
```

#### التحقق من المدخلات
```typescript
✅ الصحيح:
function divideNumbers(a: number, b: number): number {
    if (typeof a !== 'number' || typeof b !== 'number') {
        throw new Error('Both parameters must be numbers');
    }
    if (b === 0) {
        throw new Error('Division by zero is not allowed');
    }
    return a / b;
}

❌ الخطأ:
function divideNumbers(a: number, b: number): number {
    return a / b; // بدون تحقق
}
```

---

## 🔒 قواعد الحماية والأمان {#security-rules}

### التحقق من المدخلات الإجباري

#### تنظيف البيانات
```typescript
✅ الصحيح:
function sanitizeInput(input: string): string {
    if (typeof input !== 'string') {
        throw new Error('Input must be a string');
    }
    return input.trim().replace(/[<>]/g, '');
}

function processUserInput(rawInput: string): ProcessedInput {
    const sanitizedInput = sanitizeInput(rawInput);
    validateInput(sanitizedInput);
    return transformInput(sanitizedInput);
}
```

#### منع SQL Injection
```typescript
✅ الصحيح:
async function getUserById(id: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE id = ?';
    const result = await database.query(query, [id]);
    return result.rows[0] || null;
}

❌ الخطأ:
async function getUserById(id: string): Promise<User | null> {
    const query = `SELECT * FROM users WHERE id = '${id}'`; // خطر SQL Injection
    const result = await database.query(query);
    return result.rows[0] || null;
}
```

### إدارة الأسرار والمفاتيح

#### استخدام متغيرات البيئة
```typescript
✅ الصحيح:
const API_KEY = process.env.API_KEY;
if (!API_KEY) {
    throw new Error('API_KEY environment variable is required');
}

❌ الخطأ:
const API_KEY = 'sk-1234567890abcdef'; // مفتاح مكشوف في الكود
```

### Defensive Programming

#### التحقق من وجود الكائنات
```typescript
✅ الصحيح:
function getUserName(user: User | null): string {
    if (!user) {
        return 'Unknown User';
    }
    return user.name || 'No Name';
}

❌ الخطأ:
function getUserName(user: User): string {
    return user.name; // قد يسبب خطأ إذا كان user null
}
```

---

## 📚 متطلبات التوثيق {#documentation-requirements}

### JSDoc الإجباري لكل دالة

```typescript
/**
 * يحسب إجمالي سعر العناصر في السلة
 * Calculates the total price of items in the cart
 * 
 * @param items - قائمة العناصر في السلة
 * @param taxRate - معدل الضريبة (اختياري، افتراضي 0.1)
 * @returns إجمالي السعر شامل الضريبة
 * 
 * @example
 * ```typescript
 * const items = [
 *   { price: 100, quantity: 2 },
 *   { price: 50, quantity: 1 }
 * ];
 * const total = calculateCartTotal(items, 0.15);
 * console.log(total); // 287.5
 * ```
 * 
 * @throws {Error} إذا كانت قائمة العناصر فارغة
 * @throws {Error} إذا كان معدل الضريبة سالب
 */
function calculateCartTotal(items: CartItem[], taxRate: number = 0.1): number {
    if (!items || items.length === 0) {
        throw new Error('Items array cannot be empty');
    }
    if (taxRate < 0) {
        throw new Error('Tax rate cannot be negative');
    }
    
    const subtotal = items.reduce((total, item) => {
        return total + (item.price * item.quantity);
    }, 0);
    
    return subtotal * (1 + taxRate);
}
```

### تعليقات الملفات الإجبارية

```typescript
/**
 * خدمة إدارة المستخدمين
 * User Management Service
 * 
 * هذا الملف يحتوي على جميع العمليات المتعلقة بإدارة المستخدمين
 * This file contains all operations related to user management
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @requires database-service للاتصال بقاعدة البيانات
 * @requires auth-service للتحقق من الصلاحيات
 * @requires validation-utils للتحقق من صحة البيانات
 */

import { DatabaseService } from '../database/database-service';
import { AuthService } from '../auth/auth-service';
import { validateEmail, validatePassword } from '../utils/validation-utils';
```

### README.md لكل مجلد مهم

```markdown
# خدمات المستخدمين / User Services

هذا المجلد يحتوي على جميع الخدمات المتعلقة بإدارة المستخدمين.

## الملفات المتضمنة

- `user-service.ts` - الخدمة الرئيسية لإدارة المستخدمين
- `user-validation.ts` - التحقق من صحة بيانات المستخدمين  
- `user-types.ts` - تعريفات أنواع المستخدمين

## الاستخدام

```typescript
import { UserService } from './user-service';

const userService = new UserService();
const user = await userService.createUser({
    name: 'أحمد محمد',
    email: '<EMAIL>',
    password: 'securePassword123'
});
```

## الاختبارات

تشغيل اختبارات هذا المجلد:
```bash
npm test src/services/user/
```
```

---

## 🚀 معايير الأداء والاختبار {#performance-testing}

### اختبارات الوحدة الإجبارية

```typescript
// user-service.test.ts
import { UserService } from './user-service';
import { DatabaseService } from '../database/database-service';

describe('UserService', () => {
    let userService: UserService;
    let mockDatabase: jest.Mocked<DatabaseService>;

    beforeEach(() => {
        mockDatabase = {
            query: jest.fn(),
            insert: jest.fn(),
            update: jest.fn(),
            delete: jest.fn()
        } as jest.Mocked<DatabaseService>;
        
        userService = new UserService(mockDatabase);
    });

    describe('createUser', () => {
        it('should create a user with valid data', async () => {
            // Arrange
            const userData = {
                name: 'أحمد محمد',
                email: '<EMAIL>',
                password: 'securePassword123'
            };
            mockDatabase.insert.mockResolvedValue({ id: '123', ...userData });

            // Act
            const result = await userService.createUser(userData);

            // Assert
            expect(result).toBeDefined();
            expect(result.id).toBe('123');
            expect(result.email).toBe(userData.email);
            expect(mockDatabase.insert).toHaveBeenCalledWith('users', expect.any(Object));
        });

        it('should throw error for invalid email', async () => {
            // Arrange
            const userData = {
                name: 'أحمد محمد',
                email: 'invalid-email',
                password: 'securePassword123'
            };

            // Act & Assert
            await expect(userService.createUser(userData)).rejects.toThrow('Invalid email format');
        });

        it('should throw error for weak password', async () => {
            // Arrange
            const userData = {
                name: 'أحمد محمد',
                email: '<EMAIL>',
                password: '123'
            };

            // Act & Assert
            await expect(userService.createUser(userData)).rejects.toThrow('Password too weak');
        });
    });
});
```

### معايير التغطية الإجبارية

- **تغطية الكود**: يجب أن تكون أكثر من 80%
- **تغطية الفروع**: يجب أن تكون أكثر من 75%
- **تغطية الدوال**: يجب أن تكون 100%

### اختبارات الأداء

```typescript
// performance.test.ts
describe('Performance Tests', () => {
    it('should process 1000 users in less than 100ms', async () => {
        const startTime = Date.now();
        
        const users = Array.from({ length: 1000 }, (_, i) => ({
            id: `user-${i}`,
            name: `User ${i}`,
            email: `user${i}@example.com`
        }));

        const result = await userService.processUsers(users);
        
        const endTime = Date.now();
        const duration = endTime - startTime;

        expect(duration).toBeLessThan(100);
        expect(result).toHaveLength(1000);
    });
});
```

---

## ⚖️ السياسة الصارمة للالتزام {#strict-policy}

### قواعد عدم التساهل

#### 1. عدم التخمين مطلقاً
```typescript
❌ ممنوع:
// تخمين نوع البيانات
function processData(data) {
    // تخمين أن data هو array
    return data.map(item => item.name);
}

✅ الصحيح:
function processData(data: DataItem[]): string[] {
    if (!Array.isArray(data)) {
        throw new Error('Data must be an array');
    }
    return data.map((item: DataItem) => {
        if (!item.name) {
            throw new Error('Each item must have a name property');
        }
        return item.name;
    });
}
```

#### 2. عدم استخدام Magic Numbers
```typescript
❌ ممنوع:
function calculateDiscount(price: number): number {
    if (price > 100) {
        return price * 0.1; // ما هو 0.1؟
    }
    return 0;
}

✅ الصحيح:
const DISCOUNT_THRESHOLD = 100;
const DISCOUNT_RATE = 0.1;

function calculateDiscount(price: number): number {
    if (price > DISCOUNT_THRESHOLD) {
        return price * DISCOUNT_RATE;
    }
    return 0;
}
```

#### 3. عدم استخدام القيم المشفرة
```typescript
❌ ممنوع:
const config = {
    apiUrl: "https://api.example.com", // قيمة مشفرة
    timeout: 5000 // قيمة مشفرة
};

✅ الصحيح:
const config = {
    apiUrl: process.env.API_URL || "https://api.example.com",
    timeout: parseInt(process.env.API_TIMEOUT || "5000", 10)
};
```

### عقوبات انتهاك القواعد

#### انتهاكات بسيطة
- تحذير في التعليقات
- طلب إعادة كتابة الكود

#### انتهاكات متوسطة  
- رفض الكود
- طلب مراجعة شاملة

#### انتهاكات جسيمة
- إيقاف العمل
- مراجعة جميع الأكواد السابقة

---

## 🔍 آلية المراجعة الذاتية {#self-review}

### Checklist قبل كل Commit

#### ✅ فحص الكود
- [ ] جميع الدوال تحتوي على JSDoc
- [ ] جميع المتغيرات لها أنواع محددة
- [ ] لا يوجد استخدام لـ any type
- [ ] جميع الدوال أقل من 20 سطر
- [ ] جميع الملفات أقل من 200 سطر
- [ ] استخدام try-catch للعمليات الخطيرة
- [ ] التحقق من المدخلات في كل دالة public
- [ ] استخدام const/let بدلاً من var
- [ ] تسمية الملفات والمتغيرات صحيحة
- [ ] بنية المجلدات صحيحة

#### ✅ فحص الاختبارات
- [ ] كل دالة لها unit test
- [ ] تغطية الكود أكثر من 80%
- [ ] جميع الاختبارات تمر بنجاح
- [ ] اختبارات الحالات الاستثنائية موجودة
- [ ] اختبارات الأداء للدوال الحرجة

#### ✅ فحص التوثيق
- [ ] README.md محدث
- [ ] تعليقات الملفات موجودة
- [ ] أمثلة الاستخدام واضحة
- [ ] التوثيق باللغتين العربية والإنجليزية

#### ✅ فحص الأمان
- [ ] لا توجد أسرار مكشوفة في الكود
- [ ] التحقق من المدخلات موجود
- [ ] معالجة الأخطاء شاملة
- [ ] استخدام HTTPS للاتصالات الخارجية

### عملية المراجعة التلقائية

```typescript
/**
 * فحص تلقائي للكود قبل الحفظ
 * Automatic code review before save
 */
class CodeReviewChecker {
    static async performSelfReview(filePath: string): Promise<ReviewResult> {
        const checks = [
            this.checkNamingConventions,
            this.checkTypeAnnotations,
            this.checkFunctionSize,
            this.checkDocumentation,
            this.checkErrorHandling,
            this.checkSecurity
        ];

        const results = await Promise.all(
            checks.map(check => check(filePath))
        );

        return {
            passed: results.every(result => result.passed),
            issues: results.flatMap(result => result.issues),
            score: this.calculateScore(results)
        };
    }
}
```

---

## 🎯 الخلاصة

هذا الدستور يحتوي على **القواعد الإجبارية** التي يجب على أي ذكاء اصطناعي الالتزام بها عند كتابة الكود. 

### المبادئ الأساسية:
1. **لا تخمين** - كل شيء يجب أن يكون واضح ومحدد
2. **لا عشوائية** - كل قرار يجب أن يكون مبرر
3. **لا استثناءات** - القواعد تطبق على جميع الأكواد
4. **الجودة أولاً** - الكود يجب أن يكون قابل للقراءة والصيانة
5. **الأمان دائماً** - كل كود يجب أن يكون آمن

### التزام تلقائي:
من الآن فصاعداً، كل كود يتم كتابته في هذا المشروع يجب أن يلتزم بهذه القواعد تلقائياً، بدون استثناءات أو تساهل.

---

**تاريخ الإنشاء**: 2024-01-01  
**الإصدار**: 1.0.0  
**المؤلف**: AI Assistant  
**الحالة**: نافذ ومُلزم
